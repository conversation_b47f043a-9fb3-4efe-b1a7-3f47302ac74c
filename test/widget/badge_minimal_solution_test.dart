/*
 * 描述：Badge最小化解决方案测试
 * 作者：AI Assistant
 * 创建时间：2025/1/21
 */

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Badge Minimal Solution Tests', () {
    testWidgets('Badge should use unified layout without platform differences', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              constraints: const BoxConstraints(minWidth: 16, maxHeight: 16),
              child: Center(
                child: Text(
                  '99',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 10,
                    height: 1.6, // 统一的行高设置
                    color: Colors.blue,
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      final Text textWidget = tester.widget<Text>(find.text('99'));
      
      // 验证关键属性
      expect(textWidget.style?.fontSize, equals(10));
      expect(textWidget.style?.height, equals(1.6));
      expect(textWidget.textAlign, equals(TextAlign.center));
      expect(find.text('99'), findsOneWidget);
    });

    testWidgets('Container should have correct constraints', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              constraints: const BoxConstraints(minWidth: 18, maxHeight: 18),
              child: const Text('Test'),
            ),
          ),
        ),
      );

      final Container containerWidget = tester.widget<Container>(find.byType(Container));
      final BoxConstraints constraints = containerWidget.constraints as BoxConstraints;
      
      expect(constraints.minWidth, equals(18));
      expect(constraints.maxHeight, equals(18));
    });

    testWidgets('Text should render correctly with height 1.6', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Text(
              '5',
              style: TextStyle(
                fontSize: 10,
                height: 1.6,
              ),
            ),
          ),
        ),
      );

      final Text textWidget = tester.widget<Text>(find.text('5'));
      expect(textWidget.style?.height, equals(1.6));
      expect(textWidget.style?.fontSize, equals(10));
    });
  });
}
