/*
 * 描述：Badge对齐测试 - 验证fontSize=10时的居中显示
 * 作者：AI Assistant
 * 创建时间：2025/1/21
 */

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';

void main() {
  group('Badge Alignment Tests', () {
    testWidgets('Badge with fontSize=10 should be properly centered', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              constraints: const BoxConstraints(minWidth: 16, maxHeight: 16),
              padding: const EdgeInsets.only(left: 4, right: 4, top: 1, bottom: 1),
              child: SizedBox(
                height: 14, // 16 - 2*padding
                child: Align(
                  child: SmartHomeText(
                    text: '99',
                    fontSize: 10,
                    height: 1.4,
                    color: Colors.blue,
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.text('99'));
      expect(textWidget.style?.fontSize, equals(10));
      expect(textWidget.style?.height, equals(1.4));
      
      // 验证SizedBox的高度
      final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
      expect(sizedBox.height, equals(14));
    });

    testWidgets('Badge container should have correct constraints', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              constraints: const BoxConstraints(minWidth: 16, maxHeight: 16),
              child: const Text('Test'),
            ),
          ),
        ),
      );

      final containerWidget = tester.widget<Container>(find.byType(Container));
      final constraints = containerWidget.constraints as BoxConstraints;
      
      expect(constraints.minWidth, equals(16));
      expect(constraints.maxHeight, equals(16));
    });

    testWidgets('Text with height=1.4 should render correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SmartHomeText(
              text: '5',
              fontSize: 10,
              height: 1.4,
              color: Colors.red,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.text('5'));
      expect(textWidget.style?.height, equals(1.4));
      expect(textWidget.style?.fontSize, equals(10));
    });
  });
}
