/*
 * 描述：UniversalBadgeWidget 测试
 * 作者：AI Assistant
 * 创建时间：2025/1/21
 */

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_home/common/widgets/universal_badge_widget.dart';

void main() {
  group('UniversalBadgeWidget Tests', () {
    testWidgets('should render badge with correct text', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: UniversalBadgeWidget(
              text: '5',
            ),
          ),
        ),
      );

      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('should apply correct styling for cross-platform consistency', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: UniversalBadgeWidget(
              text: '10',
              fontSize: 12,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.text('10'));
      expect(textWidget.style?.fontSize, equals(12));
      expect(textWidget.style?.height, equals(1.0));
      expect(textWidget.textHeightBehavior?.leadingDistribution, 
             equals(TextLeadingDistribution.even));
    });
  });

  group('FoodCountBadgeWidget Tests', () {
    testWidgets('should display single digit correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: FoodCountBadgeWidget(count: 5),
          ),
        ),
      );

      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('should display double digit correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: FoodCountBadgeWidget(count: 25),
          ),
        ),
      );

      expect(find.text('25'), findsOneWidget);
    });

    testWidgets('should display 99+ for numbers over 99', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: FoodCountBadgeWidget(count: 150),
          ),
        ),
      );

      expect(find.text('99+'), findsOneWidget);
    });

    testWidgets('should not display anything for zero count', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: FoodCountBadgeWidget(count: 0),
          ),
        ),
      );

      expect(find.text('0'), findsNothing);
    });
  });
}
