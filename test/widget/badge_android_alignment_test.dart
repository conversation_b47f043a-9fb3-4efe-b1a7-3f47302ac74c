/*
 * 描述：Badge Android对齐测试
 * 作者：AI Assistant
 * 创建时间：2025/1/21
 */

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Badge Android Alignment Tests', () {
    testWidgets('Transform.translate should apply correct offset for Android',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Transform.translate(
              offset: Platform.isAndroid ? const Offset(0, 1.0) : Offset.zero,
              child: const Text(
                '99',
                style: TextStyle(fontSize: 10),
              ),
            ),
          ),
        ),
      );

      final Iterable<Transform> transformWidgets =
          tester.widgetList<Transform>(find.byType(Transform));
      expect(transformWidgets.isNotEmpty, isTrue);

      // 验证Transform是否正确应用
      expect(find.text('99'), findsOneWidget);
    });

    testWidgets('Text should have correct TextHeightBehavior',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Text(
              '5',
              textHeightBehavior: TextHeightBehavior(
                applyHeightToFirstAscent: false,
                applyHeightToLastDescent: false,
                leadingDistribution: TextLeadingDistribution.even,
              ),
              style: TextStyle(
                fontSize: 10,
                height: 1.0,
              ),
            ),
          ),
        ),
      );

      final Text textWidget = tester.widget<Text>(find.text('5'));
      expect(textWidget.textHeightBehavior?.applyHeightToFirstAscent,
          equals(false));
      expect(textWidget.textHeightBehavior?.applyHeightToLastDescent,
          equals(false));
      expect(textWidget.textHeightBehavior?.leadingDistribution,
          equals(TextLeadingDistribution.even));
      expect(textWidget.style?.height, equals(1.0));
    });

    testWidgets('SizedBox should have correct height',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SizedBox(
              height: 14,
              child: Text('Test'),
            ),
          ),
        ),
      );

      final SizedBox sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
      expect(sizedBox.height, equals(14));
    });
  });
}
