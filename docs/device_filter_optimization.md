# 设备过滤器排序优化文档

## 优化背景

原有的 `_updateDeviceFilter()` 方法采用嵌套排序策略：
1. 先对楼层进行排序
2. 然后对每个楼层下的房间分别排序

这种方式在楼层较多时会产生多次排序操作，影响性能。

## 优化方案

### 方案1：统一排序（已实现）

**核心思想**：收集所有房间信息后进行一次统一排序

**实现步骤**：
1. 遍历所有楼层，收集房间信息到 `_RoomInfo` 列表
2. 对整个列表进行一次排序（先按楼层，再按房间）
3. 按排序后的顺序组装 `deviceFilterMap`

### 关键代码结构

```dart
// 辅助类
class _RoomInfo {
  final FloorModel floorModel;
  final RoomModel roomModel;
  final String floorName;
  final int floorOrder;
  final int roomOrder;
}

// 收集阶段
final List<_RoomInfo> allRooms = <_RoomInfo>[];
for (final FloorModel floorModel in floorListByFamily) {
  // 收集房间信息...
}

// 排序阶段
allRooms.sort((_RoomInfo a, _RoomInfo b) {
  final int floorCompare = a.floorOrder.compareTo(b.floorOrder);
  if (floorCompare != 0) {
    return floorCompare;
  }
  return a.roomOrder.compareTo(b.roomOrder);
});

// 组装阶段
for (final _RoomInfo roomInfo in allRooms) {
  // 组装 deviceFilterMap...
}
```

## 性能对比

| 方案 | 排序次数 | 时间复杂度 | 优势 |
|------|----------|------------|------|
| 原方案 | 1次楼层 + N次房间 | O(F log F + Σ(Ri log Ri)) | 内存使用较低 |
| 新方案 | 1次统一排序 | O(R log R) | 代码清晰，性能更好 |

其中：
- F = 楼层数
- Ri = 第i层房间数  
- R = 总房间数

## 优化效果

1. **性能提升**：减少排序次数，特别是在多楼层场景下
2. **代码可读性**：逻辑更加集中和清晰
3. **可维护性**：排序逻辑独立，便于测试和修改
4. **扩展性**：未来如需跨楼层排序规则，更容易实现

## 测试建议

建议添加以下测试用例：
1. 多楼层多房间的排序正确性
2. 单楼层的处理
3. 空房间列表的处理
4. 边界情况（如 sortCode 为空或非数字）

## 注意事项

1. 保持了原有的业务逻辑不变
2. 排序规则与原方案完全一致
3. 对外接口保持不变
4. 向后兼容性良好
