# Badge字体居中显示跨平台解决方案

## 问题现象
- **iOS**: 食材数字体fontSize=10时正常居中显示
- **Android**: 食材数字体fontSize=10时显示偏上，fontSize=10.5时正常
- **容器**: maxHeight=16的固定高度容器

## 问题根本原因

### 1. 文本渲染引擎差异
- **iOS**: 文本渲染引擎对小字体的基线计算更精确
- **Android**: 在小字体（<10.5px）时，文本基线计算存在偏差，导致文本在容器中偏上显示

### 2. 容器高度限制影响
- 外层容器：`maxHeight: 16`
- 内层padding：`top: 1, bottom: 1`
- 实际文本区域：14px高度
- 当字体过小时，Android的`Center`组件无法准确计算文本边界框

### 3. 字体大小临界值
- Android在fontSize < 10.5时出现偏上问题
- 这个临界值与Android文本渲染引擎的内部算法相关

## 解决方案

### 核心思路
通过**明确指定文本容器高度**和**设置合适的行高比例**来确保跨平台一致的居中效果。

### 具体实现

```dart
child: SizedBox(
  height: 14, // 明确指定文本容器高度 (16 - 2*padding)
  child: Align(
    child: SmartHomeText(
      text: viewModel.badge,
      fontSize: 10,
      height: 1.4, // 设置行高比例，确保文本在容器中居中
      color: AppSemanticColors.item.information.primary,
    ),
  ),
),
```

### 关键参数说明

1. **SizedBox(height: 14)**
   - 明确指定文本容器的高度
   - 计算方式：外层容器高度(16) - 上下padding(1+1) = 14
   - 避免了不同平台对容器高度计算的差异

2. **height: 1.4**
   - 设置文本的行高比例为1.4倍字体大小
   - 对于fontSize=10，实际行高为14px，正好填满容器
   - 确保文本在垂直方向上完美居中

3. **Align组件**
   - 替代Center组件，提供更精确的对齐控制
   - 默认居中对齐，但在明确高度的容器中表现更稳定

## 技术优势

1. **跨平台一致性**: 通过明确的尺寸控制消除平台差异
2. **最小修改**: 只修改关键的布局和文本属性，保持原有结构
3. **可维护性**: 解决方案简洁明了，易于理解和维护
4. **性能优化**: 避免复杂的平台判断逻辑

## 验证方法

1. **视觉验证**: 在Android和iOS设备上检查食材数字是否居中
2. **单元测试**: 运行测试确保组件属性正确设置
3. **边界测试**: 测试不同数字长度（1位、2位、99+）的显示效果

## 注意事项

- 如果需要调整容器高度，记得同步调整SizedBox的height值
- height比例1.4是针对当前容器高度优化的，如有变更需重新计算
- 建议在真机上进行最终验证，模拟器可能存在渲染差异
