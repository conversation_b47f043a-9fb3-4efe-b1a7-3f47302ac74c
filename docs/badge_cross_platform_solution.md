# Badge字体居中显示跨平台解决方案

## 问题现象
- **iOS**: 食材数字体fontSize=10时正常居中显示
- **Android**: 食材数字体fontSize=10时显示偏上，fontSize=10.5时正常
- **容器**: 固定高度容器中的文本垂直对齐问题

## 问题根本原因

### 1. 文本行高与容器高度不匹配
- 默认的文本行高在小字体时无法很好地填充容器空间
- 不同平台对文本基线的计算存在细微差异
- `Center`组件依赖文本的内在尺寸进行居中，当文本过小时效果不理想

### 2. 容器尺寸调整
- 原容器：`maxHeight: 16`，现调整为：`maxHeight: 18`
- 为文本提供更充裕的垂直空间

## 解决方案

### 核心思路
通过**明确指定文本容器高度**、**精确的文本渲染控制**和**平台特定的位置微调**来确保跨平台一致的居中效果。

### 具体实现

```dart
child: SizedBox(
  height: 14, // 明确指定文本容器高度 (16 - 2*padding)
  child: Center(
    child: Transform.translate(
      offset: Platform.isAndroid
          ? const Offset(0, 1.0) // Android上向下微调1.0px
          : Offset.zero, // iOS保持原位
      child: Text(
        viewModel.badge,
        textAlign: TextAlign.center,
        textHeightBehavior: const TextHeightBehavior(
          applyHeightToFirstAscent: false,
          applyHeightToLastDescent: false,
          leadingDistribution: TextLeadingDistribution.even,
        ),
        style: TextStyle(
          fontSize: 10,
          height: 1.0, // 使用1.0的行高，让文本紧凑
          color: AppSemanticColors.item.information.primary,
          fontFamilyFallback: const <String>['PingFang SC'], // 强制使用相同字体
        ),
      ),
    ),
  ),
),
```

### 关键参数说明

1. **SizedBox(height: 14)**
   - 明确指定文本容器的高度
   - 计算方式：外层容器高度(16) - 上下padding(1+1) = 14
   - 避免了不同平台对容器高度计算的差异

2. **Transform.translate + Platform检测**
   - Android: `Offset(0, 1.0)` 向下微调1.0px
   - iOS: `Offset.zero` 保持原位
   - 精确补偿Android文本渲染引擎的基线偏差

3. **TextHeightBehavior精确控制**
   - `applyHeightToFirstAscent: false` - 不在首行应用额外高度
   - `applyHeightToLastDescent: false` - 不在末行应用额外高度
   - `leadingDistribution: TextLeadingDistribution.even` - 均匀分布行间距

4. **height: 1.0 + 强制字体**
   - 使用1.0的紧凑行高，避免额外空间
   - 强制使用PingFang SC字体确保跨平台一致性

## 技术优势

1. **跨平台一致性**: 通过明确的尺寸控制消除平台差异
2. **最小修改**: 只修改关键的布局和文本属性，保持原有结构
3. **可维护性**: 解决方案简洁明了，易于理解和维护
4. **性能优化**: 避免复杂的平台判断逻辑

## 验证方法

1. **视觉验证**: 在Android和iOS设备上检查食材数字是否居中
2. **单元测试**: 运行测试确保组件属性正确设置
3. **边界测试**: 测试不同数字长度（1位、2位、99+）的显示效果

## 注意事项

- 如果需要调整容器高度，记得同步调整SizedBox的height值
- height比例1.4是针对当前容器高度优化的，如有变更需重新计算
- 建议在真机上进行最终验证，模拟器可能存在渲染差异
