/*
 * 描述：通用Badge组件 - 解决跨平台文字显示不一致问题
 * 作者：AI Assistant
 * 创建时间：2025/1/21
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../widget_common/card_text_style.dart';

/// 通用Badge组件，解决Android和iOS平台文字显示不一致的问题
class UniversalBadgeWidget extends StatelessWidget {
  const UniversalBadgeWidget({
    super.key,
    required this.text,
    this.fontSize = 10,
    this.textColor,
    this.backgroundColor = const Color.fromRGBO(232, 247, 255, 1),
    this.borderColor = Colors.white,
    this.borderWidth = 1,
    this.borderRadius = 8,
    this.minWidth = 16,
    this.maxHeight = 16,
    this.horizontalPadding = 4,
  });

  final String text;
  final double fontSize;
  final Color? textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final double minWidth;
  final double maxHeight;
  final double horizontalPadding;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(borderWidth),
      decoration: BoxDecoration(
        color: borderColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: _calculateVerticalPadding(),
        ),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(borderRadius - borderWidth),
        ),
        constraints: BoxConstraints(
          minWidth: minWidth - (borderWidth * 2),
          maxHeight: maxHeight - (borderWidth * 2),
        ),
        child: Center(
          child: Text(
            text,
            textAlign: TextAlign.center,
            textHeightBehavior: const TextHeightBehavior(
              applyHeightToFirstAscent: false,
              applyHeightToLastDescent: false,
              leadingDistribution: TextLeadingDistribution.even,
            ),
            style: TextStyle(
              fontFamilyFallback: fontFamilyFallback(),
              fontSize: fontSize,
              height: 1.0, // 固定行高比例，确保跨平台一致性
              color: textColor ?? AppSemanticColors.item.information.primary,
              textBaseline: TextBaseline.alphabetic, // 明确指定基线
            ),
          ),
        ),
      ),
    );
  }

  /// 根据字体大小和容器高度计算合适的垂直padding
  double _calculateVerticalPadding() {
    // 基于字体大小和容器高度的动态计算
    // 这样可以确保文字在容器中垂直居中
    final double availableHeight = maxHeight - (borderWidth * 2);
    final double textHeight = fontSize * 1.0; // height = 1.0
    final double verticalSpace = availableHeight - textHeight;
    return (verticalSpace / 2).clamp(0.5, 2.0); // 限制在合理范围内
  }
}

/// 专门用于食材数量显示的Badge组件
class FoodCountBadgeWidget extends StatelessWidget {
  const FoodCountBadgeWidget({
    super.key,
    required this.count,
  });

  final int count;

  @override
  Widget build(BuildContext context) {
    // 根据数字位数决定显示样式
    final String displayText = _getDisplayText(count);
    final double minWidth = _getMinWidth(count);

    return UniversalBadgeWidget(
      text: displayText,
      minWidth: minWidth,
      textColor: AppSemanticColors.item.information.primary,
    );
  }

  String _getDisplayText(int count) {
    if (count <= 0) {
      return '';
    }
    if (count <= 99) {
      return count.toString();
    }
    return '99+';
  }

  double _getMinWidth(int count) {
    if (count < 10) {
      return 16; // 1位数：圆形
    }
    if (count <= 99) {
      return 20; // 2位数：椭圆形
    }
    return 24; // 3位数及以上：更宽的椭圆形
  }
}
