import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import '../../../../common/constant.dart';
import '../../../../widget_common/card_text_style.dart';

class NoDeviceWidget extends StatelessWidget {
  final String noDeviceIcon;
  final String noDeviceText;

  const NoDeviceWidget({
    super.key,
    required this.noDeviceIcon,
    required this.noDeviceText,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        child: Column(
          children: <Widget>[
            Image.asset(
              noDeviceIcon,
              package: SmartHomeConstant.package,
              width: 96,
              height: 96,
            ),
            const SizedBox(height: 8),
            Text(
              noDeviceText,
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontFamilyFallback: fontFamilyFallback(),
                  color: AppSemanticColors.item.secWeaken),
            )
          ],
        ),
      ),
    );
  }
}
