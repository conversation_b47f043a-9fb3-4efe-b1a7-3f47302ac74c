import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_mark_view_model.dart';

import '../../../common/smart_home_text_widget.dart';
import '../../../store/smart_home_store.dart';
import 'click_effect_circular_widget.dart';
import 'debounce_throttler/throttler_widget.dart';

class ExpandSwitchIconTextMarkWidget extends StatelessWidget {
  const ExpandSwitchIconTextMarkWidget({super.key, required this.viewModel});

  final ExpandSwitchIconTextMarkViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: Stack(
        clipBehavior: Clip.none,
        children: <Widget>[
          SizedBox(
            width: double.infinity,
            height: 52,
            child: ThrottlerWidget(
              throttlerCallback: (BuildContext context) {
                viewModel.clickCallback?.call(context);
              },
              millionSeconds: viewModel.throttlerMillionSeconds,
              child: Opacity(
                opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
                    ? ComponentOpacity.enable
                    : ComponentOpacity.disable,
                child: ClickEffectCircularWidget(
                  enable: viewModel.enable && !smartHomeStore.state.isEditState,
                  isOn: viewModel.isOn,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      _buildButtonImage(),
                      if (viewModel.text.isNotEmpty) const SizedBox(height: 6),
                      if (viewModel.text.isNotEmpty) _buildButtonText(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (viewModel.badge.isNotEmpty) _buildMark(),
        ],
      ),
    );
  }

  Widget _buildButtonImage() {
    return Image.asset(
      viewModel.icon,
      package: viewModel.packageName,
      width: 16,
      height: 16,
      color: viewModel.isOn
          ? AppSemanticColors.item.information.primary
          : AppSemanticColors.item.primary,
    );
  }

  Widget _buildButtonText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: SmartHomeText(
        text: viewModel.text,
        fontSize: 10,
        height: 1.1,
        color: viewModel.isOn
            ? AppSemanticColors.item.information.primary
            : AppSemanticColors.item.primary,
      ),
    );
  }

  Widget _buildMark() {
    return Positioned(
      right: -6,
      top: -6,
      child: Container(
        padding: const EdgeInsets.all(1),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Container(
          padding: const EdgeInsets.only(
              left: 4, right: 4, top: 1, bottom: Platform.isIOS ? 1 : 0),
          decoration: BoxDecoration(
            color: const Color.fromRGBO(232, 247, 255, 1),
            borderRadius: BorderRadius.circular(8),
          ),
          constraints: const BoxConstraints(minWidth: 16, maxHeight: 16),
          child: Container(
            height: 14,
            child: SmartHomeText(
              text: viewModel.badge,
              fontSize: 10,
              height: 1.4,
              color: AppSemanticColors.item.information.primary,
            ),
          ),
        ),
      ),
    );
  }
}
