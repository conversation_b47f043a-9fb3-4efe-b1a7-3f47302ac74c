import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_mark_view_model.dart';

import '../../../common/smart_home_text_widget.dart';
import '../../../store/smart_home_store.dart';
import 'click_effect_circular_widget.dart';
import 'debounce_throttler/throttler_widget.dart';

class ExpandSwitchIconTextMarkWidget extends StatelessWidget {
  const ExpandSwitchIconTextMarkWidget({super.key, required this.viewModel});

  final ExpandSwitchIconTextMarkViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: viewModel.expandFlex,
      child: Stack(
        clipBehavior: Clip.none,
        children: <Widget>[
          SizedBox(
            width: double.infinity,
            height: 52,
            child: ThrottlerWidget(
              throttlerCallback: (BuildContext context) {
                viewModel.clickCallback?.call(context);
              },
              millionSeconds: viewModel.throttlerMillionSeconds,
              child: Opacity(
                opacity: (viewModel.enable && !smartHomeStore.state.isEditState)
                    ? ComponentOpacity.enable
                    : ComponentOpacity.disable,
                child: ClickEffectCircularWidget(
                  enable: viewModel.enable && !smartHomeStore.state.isEditState,
                  isOn: viewModel.isOn,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      _buildButtonImage(),
                      if (viewModel.text.isNotEmpty) const SizedBox(height: 6),
                      if (viewModel.text.isNotEmpty) _buildButtonText(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (viewModel.badge.isNotEmpty) _buildMark(),
        ],
      ),
    );
  }

  Widget _buildButtonImage() {
    return Image.asset(
      viewModel.icon,
      package: viewModel.packageName,
      width: 16,
      height: 16,
      color: viewModel.isOn
          ? AppSemanticColors.item.information.primary
          : AppSemanticColors.item.primary,
    );
  }

  Widget _buildButtonText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: SmartHomeText(
        text: viewModel.text,
        fontSize: 10,
        height: 1.1,
        color: viewModel.isOn
            ? AppSemanticColors.item.information.primary
            : AppSemanticColors.item.primary,
      ),
    );
  }

  Widget _buildMark() {
    return Positioned(
      right: -7,
      top: -7,
      child: Container(
        padding: const EdgeInsets.all(1),
        constraints: const BoxConstraints(minWidth: 18, maxHeight: 18),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Container(
          padding: const EdgeInsets.only(left: 4, right: 4, top: 1, bottom: 1),
          decoration: BoxDecoration(
            color: const Color.fromRGBO(232, 247, 255, 1),
            borderRadius: BorderRadius.circular(8),
          ),
          constraints: const BoxConstraints(minWidth: 16, maxHeight: 16),
          child: SizedBox(
            height: 14, // 明确指定文本容器高度 (16 - 2*padding)
            child: Center(
              child: Transform.translate(
                offset: Platform.isAndroid
                    ? const Offset(0, 1.0) // Android上向下微调1.0px
                    : Offset.zero, // iOS保持原位
                child: Text(
                  viewModel.badge,
                  textAlign: TextAlign.center,
                  textHeightBehavior: const TextHeightBehavior(
                    applyHeightToFirstAscent: false,
                    applyHeightToLastDescent: false,
                    leadingDistribution: TextLeadingDistribution.even,
                  ),
                  style: TextStyle(
                    fontSize: 10,
                    height: 1.0, // 使用1.0的行高，让文本紧凑
                    color: AppSemanticColors.item.information.primary,
                    fontFamilyFallback: const <String>[
                      'PingFang SC'
                    ], // 强制使用相同字体
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
