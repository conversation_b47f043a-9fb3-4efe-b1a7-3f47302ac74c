import 'package:connectivity/connectivity.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:library_widgets/common/log.dart';
import 'package:smart_home/device/aggregation/aggregation_card/curtain_card/model/aggregation_curtain_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/light_card/model/aggregation_light_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_util.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/detail_room_list_item.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_manage_dialog.dart';

import '../../../../common/common_network_image_widget.dart';
import '../../../../common/constant_gio.dart';
import '../../../../common/smart_home_util.dart';
import '../../../../navigator/sliver_header_delegate.dart';
import '../../../../store/smart_home_state.dart';
import '../../../../store/smart_home_store.dart';
import '../../../../widget_common/card_text_style.dart';
import '../../../../widget_common/over_scroll_behavior.dart';
import '../../../device_info_model/smart_home_device_basic_info.dart';
import '../../../device_view_model/device_card_view_model.dart';
import '../../../device_widget/device_card.dart';
import '../../../store/device_action.dart';
import '../../aggregation_card/env_card/model/agg_env_view_model.dart';
import '../../aggregation_card/view_model/aggregation_base_view_model.dart';
import '../../aggregation_card/view_model/aggregation_devices_by_room_model.dart';
import '../../aggregation_setting/util/aggregation_setting_constant.dart';
import '../../utils/agg_utils.dart';
import '../model/supportDeviceModel.dart';
import '../utils/aggregation_presenter.dart';
import '../view_model/detail_room_list.dart';
import '../view_model/manage_room_list.dart';
import '../view_model/manage_room_list_item.dart';

class AggregationManageList extends StatefulWidget {
  final AggregationBaseViewModel vm;

  const AggregationManageList({super.key, required this.vm});

  @override
  State<AggregationManageList> createState() {
    return AggregationManageState();
  }
}

class AggregationManageState extends State<AggregationManageList> {
  final ValueKey<String> _customScrollViewKey =
      const ValueKey<String>('aggregation_custom_scroll_view_manage');
  final ScrollController _scrollController = ScrollController();
  String managePageName = 'aggregationManagePage';

  AggregationBaseViewModel? localVm;

  @override
  void initState() {
    super.initState();
    AggregationPresenter().initLoadingStatus();
    InterceptSystemBackUtil.interceptSystemBack(
        pageName: managePageName,
        callback: () {
          Navigator.pop(context);
        });
    updateLocalVm(widget.vm);
  }

  void updateLocalVm(AggregationBaseViewModel vm) {
    if (isDeviceLightAggregation(vm.deviceId)) {
      localVm = AggregationLightViewModel(device: vm.device);
    } else if (isDeviceCurtainAggregation(vm.deviceId)) {
      localVm = AggregationCurtainViewModel(device: vm.device);
    } else if (isEnvAgg(vm.deviceId)) {
      localVm = AggEnvViewModel(device: vm.device);
    }
    localVm?.roomList = <SmartHomeRoomInfo>[...vm.roomList];
    final Map<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel>
        tmpDeviceRoom =
        <SmartHomeRoomInfo, AggregationDevicesByRoomViewModel>{};
    vm.devicesByRoom.forEach((SmartHomeRoomInfo roomInfo,
        AggregationDevicesByRoomViewModel roomModel) {
      tmpDeviceRoom[roomInfo] = roomModel;
    });
    localVm?.devicesByRoom = tmpDeviceRoom;
    localVm?.roomList = <SmartHomeRoomInfo>[...vm.roomList];
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController?.dispose();
    InterceptSystemBackUtil.cancelInterceptSystemBack(managePageName);
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: () => StoreProvider<SmartHomeState>(
              store: smartHomeStore,
              child: Stack(
                children: <Widget>[
                  Scaffold(
                    backgroundColor: AppSemanticColors.background.secondary,
                    body: Padding(
                      /// 底部安全区包括BottomNavigationBar的高
                      padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).padding.bottom),
                      child: DefaultTextStyle(
                        style: TextStyle(
                          color: AppSemanticColors.item.primary,
                        ),
                        child: SizedBox(
                          height: MediaQuery.of(context).size.height +
                              MediaQuery.of(context).padding.top,
                          child: localVm == null
                              ? Container()
                              : buildManageStack(localVm!, context,
                                  (SmartHomeRoomInfo? room, String deviceId) {
                                  // 更新设备列表
                                  if (room != null) {
                                    // devices
                                    // 首先，从 devicesByRoom 中移除指定设备
                                    // 获取要移除设备所在房间的信息
                                    final MapEntry<SmartHomeRoomInfo,
                                            AggregationDevicesByRoomViewModel>
                                        roomEntry = localVm!
                                            .devicesByRoom.entries
                                            .firstWhere(
                                      (MapEntry<SmartHomeRoomInfo,
                                                  AggregationDevicesByRoomViewModel>
                                              entry) =>
                                          entry.key.roomId == room.roomId,
                                      orElse: () => MapEntry<SmartHomeRoomInfo,
                                              AggregationDevicesByRoomViewModel>(
                                          SmartHomeRoomInfo(
                                              roomId: '', roomName: ''),
                                          AggregationDevicesByRoomViewModel()),
                                    );

                                    // 如果找到了对应的房间信息
                                    if (roomEntry.key.roomId.isNotEmpty) {
                                      final AggregationDevicesByRoomViewModel
                                          roomModel = roomEntry.value;
                                      // 检查设备列表中是否包含要移除的设备
                                      if (roomModel.deviceList
                                          .contains(deviceId)) {
                                        // 移除设备
                                        roomModel.deviceList.remove(deviceId);
                                        // 如果移除设备后房间的设备列表为空
                                        if (roomModel.deviceList.isEmpty) {
                                          // 从 devicesByRoom 中移除该房间
                                          localVm!.devicesByRoom
                                              .remove(roomEntry.key);
                                          // 从 roomList 中移除该房间
                                          localVm!.roomList.removeWhere(
                                              (SmartHomeRoomInfo r) =>
                                                  r.roomId == room.roomId);
                                        }
                                      }
                                    }
                                    setState(() {});
                                  }
                                }),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ));
  }

  ScrollConfiguration buildManageStack(
      AggregationBaseViewModel viewModel,
      BuildContext context,
      void Function(SmartHomeRoomInfo? room, String deviceId)? updateCallback) {
    return ScrollConfiguration(
      behavior: NoneOverScrollBehavior(),
      child: Stack(
        children: <Widget>[
          CustomScrollView(
            key: _customScrollViewKey,
            physics: const AlwaysScrollableScrollPhysics(),
            controller: _scrollController,
            slivers: <Widget>[
              /// 头部
              HeaderWidget(
                vm: viewModel,
              ),
              RoomList(
                vm: viewModel,
                updateCallback: updateCallback,
              ),
              const SliverToBoxAdapter(child: SizedBox(height: 60)),
            ],
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: const EdgeInsets.all(16),
              width: MediaQuery.of(context).size.width,
              color: AppSemanticColors.background.secondary,
              child: GestureDetector(
                onTap: () {
                  gioAggClick(GioConst.aggDetailAdd, localVm!.deviceId);
                  if (localVm != null) {
                    showAddDevicesBottomSheet(context, localVm!.deviceId, () {
                      // 更新设备列表
                      final AggregationBaseViewModel? model = smartHomeStore
                              .state
                              .deviceState
                              .allCardViewModelMap[localVm?.deviceId]
                          as AggregationBaseViewModel?;
                      if (model is AggregationBaseViewModel) {
                        updateLocalVm(model);
                        setState(() {});
                      }
                    });
                  }
                },
                child: Container(
                  width: double.infinity,
                  height: 44,
                  decoration: BoxDecoration(
                    color: AppSemanticColors.component.primary.fill,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Image.asset(
                          'assets/images/icon_aggregation_add.webp',
                          package: 'smart_home',
                          width: 20,
                          height: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '添加设备',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            fontFamilyFallback: fontFamilyFallback(),
                            fontWeight: FontWeight.w500,
                            color: AppSemanticColors.component.primary.on,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class HeaderWidget extends StatelessWidget {
  HeaderWidget({super.key, required this.vm});

  AggregationBaseViewModel vm;

  @override
  Widget build(BuildContext context) {
    /// 状态栏高度
    final double _paddingTop = MediaQuery.of(context).padding.top;
    return SliverPersistentHeader(
      pinned: true,
      delegate: SliverHeaderDelegate.builder(
        maxHeight: 44 + _paddingTop,
        minHeight: 44 + _paddingTop,
        builder:
            (BuildContext context, double shrinkOffset, bool overlapsContent) {
          return ColoredBox(
            color: AppSemanticColors.background.secondary,
            child: Stack(
              children: <Widget>[
                Column(children: <Widget>[
                  AnnotatedRegion<SystemUiOverlayStyle>(
                      child: Container(
                          color: AppSemanticColors.background.secondary,
                          height: _paddingTop),
                      value: const SystemUiOverlayStyle(
                        systemNavigationBarColor: Colors.white,
                        systemNavigationBarIconBrightness: Brightness.dark,
                        statusBarColor: Colors.transparent,
                        statusBarBrightness: Brightness.light,
                        statusBarIconBrightness: Brightness.dark,
                      )),
                  Expanded(
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      color: AppSemanticColors.background.secondary,
                      padding: const EdgeInsets.only(left: 16, right: 16),
                      child: Stack(
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.only(top: 3),
                            child: Align(
                                alignment: Alignment.topCenter,
                                child: Text(vm.managePageTitle,
                                    style: TextStyle(
                                      fontSize: 17,
                                      fontWeight: FontWeight.w500,
                                      fontFamilyFallback: fontFamilyFallback(),
                                      color: AppSemanticColors.item.primary,
                                    ))),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 16,
                              right: 16,
                              top: 27,
                            ),
                            child: Align(
                                alignment: Alignment.topCenter,
                                child: Text(vm.managePageSubTitle,
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontFamilyFallback: fontFamilyFallback(),
                                      color: AppSemanticColors.item.secWeaken,
                                    ))),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                              top: 10,
                            ),
                            child: GestureDetector(
                              onTap: () {
                                AggregationPresenter().hideLoading();
                                Navigator.pop(context);
                              },
                              child: Image.asset(
                                'assets/images/icon_aggregation_back.webp',
                                package: 'smart_home',
                                width: 24,
                                height: 24,
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ])
              ],
            ),
          );
        },
      ),
    );
  }
}

class RoomList extends StatelessWidget {
  const RoomList({super.key, required this.vm, this.updateCallback});

  final AggregationBaseViewModel vm;
  final void Function(SmartHomeRoomInfo? room, String deviceId)? updateCallback;

  static const String removedDeviceListKey = 'removedDeviceList';

  @override
  Widget build(BuildContext context) {
    final List<AggregationRoomInfo> roomList = <AggregationRoomInfo>[];
    vm.roomList.forEach((SmartHomeRoomInfo roomInfo) {
      final AggregationDevicesByRoomViewModel? roomViewModel =
          vm.devicesByRoom[roomInfo];
      if (roomViewModel is AggregationDevicesByRoomViewModel &&
          roomViewModel.deviceList.isNotEmpty) {
        final bool showFloor = smartHomeStore.state.deviceState.cardShowFloor;
        String floorName = '', roomName = roomInfo.roomName;
        if (showFloor) {
          floorName = roomInfo.floorName;
        }
        roomList
            .add(AggregationRoomInfo(roomInfo.roomId, '$floorName$roomName'));
      }
    });
    final ManageRoomList contentRoomList = ManageRoomList(roomList);
    final List<AggregationRoomInfo> rooms = contentRoomList.roomList;
    if (contentRoomList.roomList.isEmpty) {
      return EmptyView(vm: vm);
    }
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (BuildContext context, int roomIndex) {
          final String roomId = rooms[roomIndex].roomId;
          final List<String> deviceList = <String>[];
          AggregationDevicesByRoomViewModel? roomViewModel;
          vm.devicesByRoom.forEach((SmartHomeRoomInfo roomInfo,
              AggregationDevicesByRoomViewModel model) {
            if (roomId == roomInfo.roomId) {
              deviceList.addAll(model.deviceList);
              roomViewModel = model;
            }
          });
          final ManageRoomListItem contentRoomListItem =
              ManageRoomListItem(roomId, deviceList, roomViewModel!);
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Text(
                  rooms[roomIndex].roomName,
                  style: TextStyle(
                    fontSize: 17,
                    fontFamilyFallback: fontFamilyFallback(),
                    color: AppSemanticColors.item.primary,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.only(left: 16, right: 16),
                child: CustomScrollView(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  slivers: <Widget>[
                    SliverList.builder(
                      itemBuilder: (BuildContext context, int deviceIndex) {
                        final String deviceId =
                            contentRoomListItem.deviceList[deviceIndex];
                        final DeviceCardViewModel? deviceCardViewModel =
                            smartHomeStore.state.deviceState
                                    .allCardViewModelMap[deviceId]
                                as DeviceCardViewModel?;
                        return SizedBox(
                          height: 76,
                          child: Column(
                            children: <Widget>[
                              Container(
                                height: 64,
                                padding:
                                    const EdgeInsets.only(left: 16, right: 16),
                                decoration: BoxDecoration(
                                  color: AppSemanticColors.background.primary,
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Row(
                                  children: <Widget>[
                                    GestureDetector(
                                      onTap: () {
                                        _removeClick(
                                          context,
                                          contentRoomListItem,
                                          deviceId,
                                          rooms[roomIndex],
                                          updateCallback,
                                        );
                                      },
                                      child: Image.asset(
                                        'assets/images/icon_aggregation_delete.webp',
                                        package: 'smart_home',
                                        width: 24,
                                        height: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    SizedBox(
                                        width: 32,
                                        height: 32,
                                        child: CommonNetworkRefreshImg(
                                          imageUrl: getSmallCardImgModel(
                                                  deviceCardViewModel
                                                          ?.deviceIcon ??
                                                      '')
                                              .deviceIcon,
                                          alignment: Alignment.center,
                                          errorWidget: Image.asset(
                                            'assets/icons/default_device_img.webp',
                                            package: 'smart_home',
                                            height: double.infinity,
                                          ),
                                        )),
                                    const SizedBox(
                                      width: 12,
                                    ),
                                    Expanded(
                                      child: SizedBox(
                                        width: double.infinity,
                                        child: Text(
                                          deviceCardViewModel?.deviceName ?? '',
                                          style: TextStyle(
                                              fontSize: 16,
                                              fontFamilyFallback:
                                                  fontFamilyFallback(),
                                              fontWeight: FontWeight.w500,
                                              color: AppSemanticColors
                                                  .item.primary),
                                          maxLines: 1,
                                          softWrap: false,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                        );
                      },
                      itemCount: contentRoomListItem.deviceList.length,
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 8,
              ),
            ],
          );
        },
        childCount: rooms.length,
      ),
    );
  }

  Future<void> _removeClick(
      BuildContext? context,
      ManageRoomListItem contentRoomListItem,
      String deviceId,
      AggregationRoomInfo room,
      void Function(SmartHomeRoomInfo? room, String deviceId)?
          updateCallback) async {
    if (AggregationPresenter().loadingStatus == AggLoadingStatus.loading) {
      return;
    }
    gioAggClick(GioConst.aggDetailRemove, vm.deviceId);
    try {
      // 检查网络连接
      final ConnectivityResult result =
          await Connectivity().checkConnectivity();
      if (result == ConnectivityResult.none) {
        ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
        return;
      }
      // 显示加载动画
      if (context != null && context.mounted) {
        AggregationPresenter().showLoading(context);
      }

      final List<String> allDeviceIds = <String>[];
      SmartHomeRoomInfo? roomInfo;
      vm.devicesByRoom.forEach((SmartHomeRoomInfo room,
          AggregationDevicesByRoomViewModel deviceRoomModel) {
        allDeviceIds.addAll(deviceRoomModel.deviceList);
        if (room.roomId == contentRoomListItem.roomId) {
          roomInfo = room;
        }
      });
      allDeviceIds.remove(deviceId);

      DevLogger.info(
          tag: 'AggregationManageList',
          msg: 'AggregationManageList _removeClick, aggregationParentId:'
              '${vm.deviceId}, allDeviceIds: $allDeviceIds');

      // 保存聚合设备ID列表
      await AggregationPresenter.saveAggregationIdList(
          vm.deviceId, allDeviceIds.toList());
      smartHomeStore.dispatch(RemoveAggDeviceAction(
          aggregationId: vm.deviceId,
          allDevices: allDeviceIds.toList(),
          deleteDevice: deviceId,
          sortedAggList: _saveAggSort(vm, deviceId)));
      if (updateCallback != null) {
        updateCallback(roomInfo, deviceId);
      }
      await AggregationPresenter.saveAggSort(
          sortData: _aggDeleteRequestData(vm, deviceId));
      AggregationPresenter().hideLoading();
    } catch (e) {
      DevLogger.error(
          tag: 'AggregationManageList', msg: '_removeClick error $e');
      AggregationPresenter().hideLoading();
    }
  }

  AggDeviceData _saveAggSort(
      AggregationBaseViewModel vm, String removedDeviceId) {
    final Map<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel> rooms =
        vm.devicesByRoom;
    final Map<String, List<String>> sortMap = <String, List<String>>{};
    rooms.entries.forEach(
        (MapEntry<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel> entry) {
      if (entry.value.deviceList.contains(removedDeviceId)) {
        final List<String> sortList = <String>[...entry.value.deviceList];
        sortList.remove(removedDeviceId);
        if (sortList.isNotEmpty) {
          sortMap[entry.key.roomId] = sortList;
        }
      } else {
        sortMap[entry.key.roomId] = entry.value.deviceList;
      }
    });
    return AggregationPresenter.createAggDeviceData(vm.deviceId, sortMap);
  }

  AggDeviceData _aggDeleteRequestData(
      AggregationBaseViewModel vm, String removedDeviceId) {
    final Map<String, List<String>> sortMap = <String, List<String>>{};
    sortMap[removedDeviceListKey] = <String>[removedDeviceId];
    return AggregationPresenter.createAggDeviceData(vm.deviceId, sortMap);
  }
}

class EmptyView extends StatelessWidget {
  const EmptyView({super.key, required this.vm});

  final AggregationBaseViewModel vm;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        color: AppSemanticColors.background.secondary,
        height: MediaQuery.of(context).size.height -
            145 -
            MediaQuery.of(context).padding.top,
        child: Center(
          child: SizedBox(
              height: 124,
              width: 84,
              child: Column(
                children: <Widget>[
                  Image.asset(
                    'assets/images/icon_aggregation_detail_no_device.webp',
                    package: 'smart_home',
                    width: 96,
                    height: 96,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    vm.manageListEmpty,
                    style: TextStyle(
                        fontSize: 14,
                        fontFamilyFallback: fontFamilyFallback(),
                        color: AppSemanticColors.item.secWeaken),
                  )
                ],
              )),
        ),
      ),
    );
  }
}
