import 'dart:async';

import 'package:connectivity/connectivity.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/service/switch_status_response_model.dart';
import 'package:smart_home/navigator/family/store/family_action.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/preference_setting/preference_setting_presenter.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/user.dart';

import '../../../common/smart_home_util.dart';
import '../../../service/http_service.dart';
import '../../../store/smart_home_store.dart';
import '../../../user/user_action.dart';
import '../agg_store/aggregation_action.dart';
import '../utils/agg_utils.dart';
import 'aggregation_setting_content_widget.dart';
import 'util/aggregation_setting_constant.dart';

class AggregationSettingSheet extends StatefulWidget {
  const AggregationSettingSheet(
      {super.key, required this.familyId, required this.roomId});

  final String familyId;
  final String roomId;

  @override
  State<AggregationSettingSheet> createState() {
    return AggregationSettingState();
  }
}

class AggregationSettingState extends State<AggregationSettingSheet> {
  String settingSheetName = 'aggregationSettingSheet';
  final PreferenceSettingPresenter _preferenceSettingPresenter =
      PreferenceSettingPresenter();

  @override
  void initState() {
    super.initState();
    ToastHelper.init(context);
    initData();
  }

  Future<void> initData() async {
    final LoginStatus loginStatus = await User.getLoginStatus();
    smartHomeStore
        .dispatch(UpdateLoginStatusAction(isLogin: loginStatus.isLogin));

    FamilyActionModel familyActionModel =
        FamilyActionModel(familyId: '', familyName: '', memberType: null);
    try {
      final FamilyModel familyModel = await Family.getCurrentFamily();
      familyActionModel = FamilyActionModel(
          familyId: familyModel.familyId,
          familyName: familyModel.info.familyName,
          memberType: familyModel.memberType);
    } catch (e) {
      DevLogger.error(
          tag: 'AggregationSetting',
          msg: 'initData Family.getCurrentFamily() err:$e');
    }
    smartHomeStore
        .dispatch(UpdateAppCurrentFamilyInfoAction(familyActionModel));

    _getSwitchStatus();
    // 查询仪表盘偏好设置
    _preferenceSettingPresenter.getWholeHousePreferenceSetting();
  }

  @override
  void dispose() {
    InterceptSystemBackUtil.cancelInterceptSystemBack(settingSheetName);
    super.dispose();
  }

  Future<void> _getSwitchStatus() async {
    final String familyId = smartHomeStore.state.familyState.familyId;
    final SwitchStatusResponseModel? result =
        await HttpService.queryAggregationSwitchStatus(familyId);
    if (result is SwitchStatusResponseModel) {
      smartHomeStore.dispatch(
          UpdateAggregationSettingListAction(switchStatusData: result.data));
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: () => Scaffold(
            backgroundColor: AppSemanticColors.background.secondary,
            appBar: buildAggCommonVdnHeader(
              context: context,
              title: AggregationSettingConstant.aggSettingSheetTitle,
            ),
            body: StoreProvider<SmartHomeState>(
              store: smartHomeStore,
              child: _buildContentWidget(widget.familyId, widget.roomId),
            )));
  }
}

void showAggregationSettingSheet(BuildContext context) {
  Connectivity().checkConnectivity().then((ConnectivityResult result) {
    if (result == ConnectivityResult.none) {
      ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
    } else {
      Navigator.push(
          context,
          MaterialPageRoute<AggregationSettingSheet>(
              builder: (BuildContext context) => AggregationSettingSheet(
                    familyId: smartHomeStore.state.familyState.familyId,
                    roomId: smartHomeStore.state.deviceState.selectedRoomId,
                  )));
    }
  });
}

// 构建设置页面内容Widget
Widget _buildContentWidget(String familyId, String roomId) {
  return StoreConnector<SmartHomeState, AggregationSettingListViewModel>(
    distinct: true,
    converter: (Store<SmartHomeState> store) {
      return AggregationSettingListViewModel(
        aggregationCardList: _getAggregationCardList(store),
        roomId: roomId,
        familyId: familyId,
      );
    },
    builder: (BuildContext context, AggregationSettingListViewModel vm) {
      return AggregationSettingContentWidget(vm: vm);
    },
  );
}

List<AggregationSettingBaseViewModel> _getAggregationCardList(
    Store<SmartHomeState> store) {
  final Map<String, AggregationSettingBaseViewModel> switchViewModelMap =
      store.state.aggregationState.switchViewModelMap;

  return <AggregationSettingBaseViewModel>[
    _getSwitchViewModel(
        switchViewModelMap,
        AggregationSettingConstant.agg_light_id,
        AggregationSettingConstant.aggLightingSettingName),
    _getSwitchViewModel(
        switchViewModelMap,
        AggregationSettingConstant.agg_curtain_id,
        AggregationSettingConstant.aggCurtainSettingName),
    _getSwitchViewModel(switchViewModelMap, AggregationSettingConstant.env_id,
        AggregationSettingConstant.aggEnvSettingName),
    _getSwitchViewModel(
        switchViewModelMap,
        AggregationSettingConstant.camera_id,
        AggregationSettingConstant.aggCameraSettingName),
    _getSwitchViewModel(
        switchViewModelMap,
        AggregationSettingConstant.offline_id,
        AggregationSettingConstant.aggOfflineSettingName,
        AggregationSettingConstant.aggOfflineSettingSub),
    _getSwitchViewModel(
        switchViewModelMap,
        AggregationSettingConstant.non_net_id,
        AggregationSettingConstant.aggNonnetSettingName,
        AggregationSettingConstant.aggNonnetSettingSub),
  ];
}

AggregationSettingBaseViewModel _getSwitchViewModel(
  Map<String, AggregationSettingBaseViewModel> switchViewModelMap,
  String id,
  String name, [
  String subTitle = '',
]) {
  return switchViewModelMap[id] ??
      AggregationSettingBaseViewModel(
        id: id,
        isSelected: false,
        name: name,
        subTitle: subTitle,
      );
}

class AggregationSettingBaseViewModel {
  final String id;
  final bool isSelected;
  final String name;
  final String subTitle;

  AggregationSettingBaseViewModel({
    required this.id,
    required this.isSelected,
    required this.name,
    this.subTitle = '',
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AggregationSettingBaseViewModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          isSelected == other.isSelected &&
          name == other.name &&
          subTitle == other.subTitle;

  @override
  int get hashCode =>
      id.hashCode ^ isSelected.hashCode ^ subTitle.hashCode ^ name.hashCode;

  @override
  String toString() {
    return 'CreateQuickBaseViewModel{id: $id,'
        ' isSelected: $isSelected, name: $name, subTitle: $subTitle}';
  }
}

class AggregationSettingListViewModel {
  List<AggregationSettingBaseViewModel> aggregationCardList =
      <AggregationSettingBaseViewModel>[];
  String roomId = '';
  String familyId = '';

  AggregationSettingListViewModel({
    required this.aggregationCardList,
    required this.roomId,
    required this.familyId,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AggregationSettingListViewModel &&
          runtimeType == other.runtimeType &&
          roomId == other.roomId &&
          familyId == other.familyId &&
          listEquals(aggregationCardList, other.aggregationCardList);

  @override
  int get hashCode =>
      listHashCode(aggregationCardList) ^ roomId.hashCode ^ familyId.hashCode;

  @override
  String toString() {
    return 'CreateAggregationSettingListViewModel{aggregationCardList: $aggregationCardList, '
        'roomId: $roomId, familyId: $familyId}';
  }
}

Function aggregationDebounce<T>(T? func,
    [Duration delay = const Duration(milliseconds: 2000),
    bool immediately = true]) {
  Timer? timer;
  void target() {
    if (timer?.isActive ?? false) {
      timer?.cancel();
    }
    // 立即执行
    if (immediately) {
      // 没有定时器，立即执行
      final bool callNow = timer == null;
      // 给定时器赋值
      timer = Timer(delay, () {
        timer!.cancel();
        timer = null;
      });
      if (callNow) {
        if (func is Function) {
          func.call();
        }
      }
    } else {
      timer = Timer(delay, () {
        if (func is Function) {
          func.call();
        }
      });
    }
  }

  return target;
}
