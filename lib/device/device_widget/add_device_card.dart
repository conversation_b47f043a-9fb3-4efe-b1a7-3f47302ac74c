import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:lottie/lottie.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/device_view_model/add_device_card_view_model.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../common/constant.dart';
import '../../store/smart_home_state.dart';

class AddDeviceCard extends StatelessWidget {
  final AddDeviceCardViewModel viewModel;

  const AddDeviceCard({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: !smartHomeStore.state.isEditState,
      child: GestureDetector(
        onTap: () {
          if (!smartHomeStore.state.isEditState) {
            viewModel.cardClick();
          }
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.all(
            Radius.circular(22),
          ),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _cardTitleWidget(),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: Image.asset(
                    viewModel.buttonIconUrl,
                    width: 32,
                    height: 32,
                    package: 'smart_home',
                  ),
                ),
                const SizedBox(height: 8),
                Align(
                  child: SmartHomeText(
                    text: viewModel.buttonText,
                    fontSize: 12,
                    height: 1,
                    color: const Color(0xff999999),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _cardTitleWidget() {
    return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(left: 16, top: 16),
            child: SmartHomeText(
                text: viewModel.title,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                height: 1,
                color: const Color(0xff111111)),
          ),
          _summerActivityWidget()
        ]);
  }

  Widget _summerActivityWidget() {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return store.state.operatingActivityState.summerActivity;
        },
        builder: (BuildContext context, bool isVisible) {
          return Visibility(
            visible: isVisible,
            child: Padding(
              padding: const EdgeInsets.only(top: 5),
              child: SizedBox(
                  height: 34,
                  child: Lottie.asset(viewModel.activityJsonPath,
                      package: SmartHomeConstant.package)),
            ),
          );
        });
  }
}
