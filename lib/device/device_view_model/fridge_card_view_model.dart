import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/component/view_model/expand_label_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_mark_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/fridge_foodnums/fridge_foodnum_presenter.dart';

class FridgeCardViewModel extends DeviceCardViewModel {
  FridgeCardViewModel({required super.device});

  String get refrigeratorTemperatureCKey => 'refrigeratorTemperatureC';

  String get freezerTemperatureCKey => 'freezerTemperatureC';

  static const String _emptyMarkContent = '';
  static const String _moreThan99MarkContent = '99+';

  SmartHomeDeviceAttribute? get _fridgeFoodNumAttribute {
    return device.attributeMap[FridgeFoodNumPresenter.fridgeFoodNumKey];
  }

  SmartHomeDeviceAttribute? get _fridgeFoodManageUrlAttribute {
    return device.attributeMap[FridgeFoodNumPresenter.fridgeFoodManageUrlKey];
  }

  SmartHomeDeviceAttribute? get _refrigeratorTemperatureCAttribute {
    return device.attributeMap[refrigeratorTemperatureCKey];
  }

  SmartHomeDeviceAttribute? get _freezerTemperatureCAttribute {
    return device.attributeMap[freezerTemperatureCKey];
  }

  bool get componentEnable {
    return !loading && !deviceOfflineOrPowerOff;
  }

  @override
  bool supportDeviceAlarm() {
    return false;
  }

  @override
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap {
    const String name = '冰箱';

    final ComponentBaseViewModel? viewModelL = label1();
    final ComponentBaseViewModel? viewModelR = label2();

    // 冰箱特殊处理-- 两个属性都异常时，返回null--不展示大卡片.
    if (viewModelL == null && viewModelR == null) {
      return null;
    }

    final LargeDeviceCardFunctionSet functionSet = LargeDeviceCardFunctionSet(
        name: name,
        componentViewModelList: <ComponentBaseViewModel>[
          if (viewModelL != null) viewModelL,
          if (viewModelR != null) viewModelR,
          _foodManage,
        ]);
    return <String, LargeDeviceCardFunctionSet>{name: functionSet};
  }

  @override
  String get largeCardStatus {
    return '';
  }

  ComponentBaseViewModel? label1() {
    final SmartHomeDeviceAttribute? attribute =
        _refrigeratorTemperatureCAttribute;
    if (attribute == null) {
      return null;
    }
    const String desc = '冷藏室';
    final String value = attribute.value;
    return ExpandLabelViewModel(
        value: componentEnable && value.isNotEmpty ? value : '--',
        unit: componentEnable && value.isNotEmpty ? '°C' : '',
        desc: desc,
        enable: componentEnable);
  }

  ComponentBaseViewModel? label2() {
    final SmartHomeDeviceAttribute? attribute = _freezerTemperatureCAttribute;
    if (attribute == null) {
      return null;
    }
    const String desc = '冷冻室';
    final String value = attribute.value;
    return ExpandLabelViewModel(
        value: componentEnable && value.isNotEmpty ? value : '--',
        unit: componentEnable && value.isNotEmpty ? '°C' : '',
        desc: desc,
        enable: componentEnable);
  }

  ComponentBaseViewModel get _foodManage {
    final bool compEnable = !(deviceOffline || alarm || loading);

    const String desc = '食材管理';

    final String foodNum = deviceOffline
        ? _emptyMarkContent
        : _fridgeFoodNumAttribute?.value ?? _emptyMarkContent;
    final String manageDetailUrl = _fridgeFoodManageUrlAttribute?.value ?? '';

    final ComponentBaseViewModel foodManageViewModel =
        ExpandSwitchIconTextMarkViewModel(
            text: desc,
            icon: 'assets/components/fridge_food_manage_icon.webp',
            isOn: false,
            enable: compEnable,
            badge: '99', //_formatMarkContent(foodNum),
            clickCallback: (BuildContext? context) {
              _onFoodManageClick(manageDetailUrl);
            });

    return foodManageViewModel;
  }

  void _onFoodManageClick(String manageDetailUrl) {
    checkDeviceState(checkPowerOff: false, checkOffline: false)
        .then((bool pass) {
      if (pass) {
        DevLogger.debug(
            tag: SmartHomeConstant.package,
            msg:
                'FridgeCardViewModel checkDeviceState manageDetailUrl:$manageDetailUrl');
        if (manageDetailUrl.isEmpty) {
          final String defaultFoodManagePageUrl =
              FridgeFoodNumPresenter.buildFoodManageDefaultPageUrl(deviceId);
          DevLogger.debug(
              tag: SmartHomeConstant.package,
              msg:
                  'FridgeCardViewModel checkDeviceState manageDetailUrl is empty, jump to defaultFoodManagePageUrl:$defaultFoodManagePageUrl');
          goToPageWithDebounce(defaultFoodManagePageUrl);
          return;
        }
        goToPageWithDebounce(manageDetailUrl);
      }
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'FridgeCardViewModel checkDeviceState err:$err');
    });
  }

  String _formatMarkContent(String mark) {
    if (mark.isEmpty) {
      return _emptyMarkContent;
    }
    final int num = int.tryParse(mark) ?? 0;
    if (num == 0) {
      return _emptyMarkContent;
    } else if (num > 99) {
      return _moreThan99MarkContent;
    }

    return mark;
  }
}
