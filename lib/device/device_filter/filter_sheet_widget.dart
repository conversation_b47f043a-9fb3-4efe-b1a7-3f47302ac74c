import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/device_filter/device_filter_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/device/store/device_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../common/component_constant.dart';
import '../../common/constant_gio.dart';

class FilterSheetWidget extends StatefulWidget {
  const FilterSheetWidget({super.key});

  @override
  State<FilterSheetWidget> createState() => _FilterSheetWidget();
}

class _FilterSheetWidget extends State<FilterSheetWidget> {
  //不变的State的值
  List<String> allSpaceList = <String>[];
  LinkedHashMap<String, DeviceFilterModel> deviceFilterMap =
      LinkedHashMap<String, DeviceFilterModel>();
  bool cardShowFloor = false;

  static const String spaceTitle = '空间';
  static const String roomManagerTitle = '房间管理';
  static const String roomManagerIcon = 'assets/icons/icon_setting.webp';

  //变化的临时变量
  String tempFloor = '';
  String tempRoom = '';
  String tempRoomId = '';
  String tempCategory = '';
  String tempSpace = '';
  String familyId = '';
  List<String> tempCategoryList = <String>[];

  @override
  void initState() {
    super.initState();
    final DeviceState deviceState = smartHomeStore.state.deviceState;
    tempFloor = deviceState.selectedFloor;
    tempRoom = deviceState.selectedRoom;
    tempRoomId = deviceState.selectedRoomId;
    tempCategory = deviceState.selectedDeviceCategory;
    cardShowFloor = deviceState.cardShowFloor;
    familyId = smartHomeStore.state.familyState.familyId;

    deviceFilterMap = deviceState.deviceFilterMap;
    allSpaceList = deviceFilterMap.keys.toList();

    if (tempFloor == SmartHomeConstant.deviceFilterSelectAll &&
        tempRoom == SmartHomeConstant.deviceFilterSelectAll) {
      tempSpace = SmartHomeConstant.deviceFilterHouse;
    } else {
      tempSpace = cardShowFloor ? tempFloor + tempRoom : tempRoom;
    }

    tempCategoryList =
        deviceFilterMap[tempSpace]?.categorySet.toList() ?? <String>[];
  }

  Widget _filterContentTitle(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: ComponentPadding.cardLarge),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            text,
            style: TextStyle(
              color: AppSemanticColors.item.secondary,
              fontSize: 12,
            ),
          ),
          Visibility(visible: text == spaceTitle, child: _roomManagerWidget()),
        ],
      ),
    );
  }

  Widget _roomManagerWidget() {
    return GestureDetector(
      onTap: () {
        goToPageWithDebounce(SmartHomeConstant.roomManager);
        gioTrack(GioConst.filterRoomManage);
        Navigator.pop(context);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Image.asset(
            roomManagerIcon,
            package: SmartHomeConstant.package,
            width: 16,
            height: 16,
          ),
          const SizedBox(
            width: 4,
          ),
          Text(
            roomManagerTitle,
            style: TextStyle(
              color: AppSemanticColors.item.priWeaken,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _filterContent(),
        Padding(
          padding:
              const EdgeInsets.symmetric(vertical: ComponentPadding.largeTop),
          child: _filterButtonWidget(),
        )
      ],
    );
  }

  Widget _filterContent() {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxHeight: 511),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _filterContentTitle(spaceTitle),
            _filterContentBody(allSpaceList, tempSpace, FilterListType.space),
            const SizedBox(height: ComponentGap.group),
            _filterContentTitle('设备类型'),
            _filterContentBody(
                tempCategoryList, tempCategory, FilterListType.deviceCategory),
            const SizedBox(height: ComponentMargin.pageTop),
          ],
        ),
      ),
    );
  }

  static const int kMaxSpaceNameLength = 6;

  //填充内容
  Widget _filterContentBody(List<String> listData, String selectedValue,
      FilterListType filterListType) {
    return GridView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: listData.length,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: ComponentGap.component,
            crossAxisSpacing: ComponentGap.component,
            mainAxisExtent: 44),
        itemBuilder: (BuildContext context, int index) {
          final String value = listData[index];
          final String displayText = getDisplayText(value, filterListType);

          final Color backGroundColor = selectedValue == value
              ? AppSemanticColors.component.information.fill
              : AppSemanticColors.component.secondary.invert;
          final Color borderColor = selectedValue == value
              ? AppSemanticColors.component.information.on
              : Colors.transparent;
          final Color textColor = selectedValue == value
              ? AppSemanticColors.component.information.on
              : AppSemanticColors.component.secondary.on;
          return Container(
            alignment: Alignment.center,
            child: GestureDetector(
              onTap: () {
                _updateCurrentSelectedFilterInfo(
                    filterListType, listData[index]);
              },
              child: Container(
                height: 44,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: backGroundColor,
                  borderRadius:
                      BorderRadius.circular(ComponentRadius.componentSmall),
                  border: Border.all(color: borderColor),
                ),
                child: Text(
                  displayText,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          );
        });
  }

  String getDisplayText(String value, FilterListType filterListType) {
    if (value == SmartHomeConstant.shareDeviceFlag) {
      return SmartHomeConstant.shareDeviceTabText;
    }

    if (filterListType == FilterListType.space && value.length > kMaxSpaceNameLength) {
      return '${value.substring(0, kMaxSpaceNameLength)}...';
    }

    return value;
  }

  //处理点击事件
  void _updateCurrentSelectedFilterInfo(FilterListType type, String selected) {
    if (type == FilterListType.space) {
      final DeviceFilterModel filterModel = deviceFilterMap[selected] ??
          DeviceFilterModel(
              SmartHomeConstant.deviceFilterSelectAll,
              SmartHomeConstant.deviceFilterSelectAll,
              familyId,
              LinkedHashSet<String>());
      tempFloor = filterModel.floorName;
      tempRoom = filterModel.roomName;
      tempRoomId = filterModel.roomId;
      tempCategoryList = filterModel.categorySet.toList();

      if (tempCategoryList.isNotEmpty) {
        if (!tempCategoryList.contains(tempCategory)) {
          tempCategory = tempCategoryList.first;
        }
      } else {
        tempCategory = SmartHomeConstant.deviceFilterSelectAll;
      }
      if (tempFloor == SmartHomeConstant.deviceFilterSelectAll &&
          tempRoom == SmartHomeConstant.deviceFilterSelectAll) {
        tempSpace = SmartHomeConstant.deviceFilterHouse;
      } else {
        tempSpace = cardShowFloor ? tempFloor + tempRoom : tempRoom;
      }
    } else if (type == FilterListType.deviceCategory) {
      tempCategory = selected;
      gioTrack('MB37002');
    }

    if (mounted) {
      setState(() {});
    }
  }

  Widget _filterButtonWidget() {
    return DialogComponents.buildBottomActions(<Widget>[
      ButtonFill(
        text: '重置',
        type: ButtonType.secondary,
        enable: !_isAllSelected(),
        callback: () {
          _handleReset();
        },
      ),
      ButtonFill(
          text: '确定',
          callback: () {
            _handleConfirm();
          })
    ]);
  }

  bool _isAllSelected() {
    return tempSpace == SmartHomeConstant.deviceFilterHouse &&
        tempCategory == SmartHomeConstant.deviceFilterSelectAll;
  }

  void _handleReset() {
    gioTrack('MB37005');
    tempSpace = SmartHomeConstant.deviceFilterHouse;
    tempFloor = SmartHomeConstant.deviceFilterSelectAll;
    tempRoom = SmartHomeConstant.deviceFilterSelectAll;
    tempRoomId = smartHomeStore.state.familyState.familyId;
    tempCategory = SmartHomeConstant.deviceFilterSelectAll;

    tempCategoryList =
        deviceFilterMap[tempSpace]?.categorySet.toList() ?? <String>[];
    if (mounted) {
      setState(() {});
    }
  }

  void _handleConfirm() {
    gioTrack('MB38340',
        <String, dynamic>{'sourceName': tempCategory, 'RoomName': tempSpace});
    smartHomeStore.dispatch(DeviceFilterConfirmAction(
        tempRoom, tempFloor, tempCategory, tempSpace, tempRoomId));
    Navigator.pop(context);
  }
}

enum FilterListType { deviceCategory, space }
